-- =====================================================
-- SPORTS WEBSITE DATABASE - CSV DATA IMPORT
-- =====================================================
-- This file contains SQL INSERT statements generated from CSV files
-- All data is imported from the 9 CSV files in the root directory
-- Images are using online URLs from reliable sources (no local paths)
-- =====================================================

-- =====================================================
-- DROP AND RECREATE ALL TABLES
-- =====================================================
SET FOREIGN_KEY_CHECKS = 0;

-- Drop all tables if they exist
DROP TABLE IF EXISTS fantasy_team_players;
DROP TABLE IF EXISTS fantasy_teams;
DROP TABLE IF EXISTS fantasy_leagues;
DROP TABLE IF EXISTS game_schedules;
DROP TABLE IF EXISTS news_articles;
DROP TABLE IF EXISTS players;
DROP TABLE IF EXISTS teams;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS sports_categories;
DROP TABLE IF EXISTS sessions;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- CREATE ALL TABLES
-- =====================================================

-- Sports Categories Table
CREATE TABLE sports_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(255),
    color_code VARCHAR(7) DEFAULT '#007bff',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_name (name),
    INDEX idx_active (is_active)
);

-- Users Table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    profile_image VARCHAR(255) DEFAULT '/public/images/default-avatar.jpg',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- Teams Table
CREATE TABLE teams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    logo VARCHAR(255) DEFAULT 'https://cdn-icons-png.flaticon.com/512/53/53283.png',
    category_id INT,
    country VARCHAR(100),
    description TEXT,
    founded_year INT,
    stadium VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (category_id) REFERENCES sports_categories(id) ON DELETE SET NULL,
    INDEX idx_category (category_id),
    INDEX idx_name (name),
    INDEX idx_country (country)
);

-- Players Table
CREATE TABLE players (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    team_id INT,
    position VARCHAR(50),
    jersey_number INT,
    image VARCHAR(255) DEFAULT '/public/images/players/default-player.jpg',
    stats JSON,
    price DECIMAL(8,2) DEFAULT 10.00,
    points INT DEFAULT 0,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL,
    INDEX idx_team (team_id),
    INDEX idx_name (name),
    INDEX idx_position (position),
    INDEX idx_available (is_available)
);

-- News Articles Table
CREATE TABLE news_articles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt VARCHAR(500),
    image VARCHAR(255) DEFAULT 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=600&fit=crop',
    author VARCHAR(100),
    category_id INT,
    tags JSON,
    view_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('draft', 'published', 'archived') DEFAULT 'published',
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (category_id) REFERENCES sports_categories(id) ON DELETE SET NULL,
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_published (published_at)
);

-- Fantasy Leagues Table
CREATE TABLE fantasy_leagues (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category_id INT,
    start_date DATE,
    end_date DATE,
    max_teams INT DEFAULT 12,
    entry_fee DECIMAL(8,2) DEFAULT 0.00,
    prize_pool DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('upcoming', 'active', 'completed') DEFAULT 'upcoming',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (category_id) REFERENCES sports_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date)
);

-- Game Schedules Table
CREATE TABLE game_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    home_team_id INT,
    away_team_id INT,
    game_time TIMESTAMP,
    venue VARCHAR(255),
    status ENUM('scheduled', 'live', 'completed', 'postponed', 'cancelled') DEFAULT 'scheduled',
    score_home INT NULL,
    score_away INT NULL,
    category_id INT,
    stream_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (home_team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (away_team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES sports_categories(id) ON DELETE SET NULL,
    INDEX idx_home_team (home_team_id),
    INDEX idx_away_team (away_team_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_game_time (game_time)
);

-- Fantasy Teams Table
CREATE TABLE fantasy_teams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    user_id INT NOT NULL,
    league_id INT,
    sport_id INT,
    budget DECIMAL(10,2) DEFAULT 100.00,
    current_value DECIMAL(10,2) DEFAULT 0.00,
    total_points INT DEFAULT 0,
    points INT DEFAULT 0,
    team_rank INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE SET NULL,
    FOREIGN KEY (sport_id) REFERENCES sports_categories(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_league (league_id),
    INDEX idx_sport (sport_id),
    INDEX idx_active (is_active)
);

-- Fantasy Team Players Junction Table
CREATE TABLE fantasy_team_players (
    id INT PRIMARY KEY AUTO_INCREMENT,
    team_id INT NOT NULL,
    player_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (team_id) REFERENCES fantasy_teams(id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    UNIQUE KEY unique_team_player (team_id, player_id),
    INDEX idx_team (team_id),
    INDEX idx_player (player_id)
);

-- Sessions Table (for session management)
CREATE TABLE sessions (
    session_id VARCHAR(128) COLLATE utf8mb4_bin NOT NULL,
    expires INT(11) UNSIGNED NOT NULL,
    data MEDIUMTEXT COLLATE utf8mb4_bin,
    PRIMARY KEY (session_id)
);

-- =====================================================
-- 1. SPORTS CATEGORIES
-- =====================================================
INSERT INTO sports_categories (id, name, description, icon, color_code, is_active, created_at) VALUES
(1, 'Football', 'Association football, also known as soccer', 'https://cdn-icons-png.flaticon.com/512/53/53283.png', '#28a745', TRUE, '2025-05-21 18:44:31'),
(2, 'Basketball', 'Basketball is a team sport', 'https://cdn-icons-png.flaticon.com/512/167/167739.png', '#fd7e14', TRUE, '2025-05-21 18:44:31'),
(3, 'Cricket', 'Cricket is a bat-and-ball game', 'https://cdn-icons-png.flaticon.com/512/2736/2736389.png', '#20c997', TRUE, '2025-05-21 18:44:31'),
(4, 'Tennis', 'Tennis is a racket sport', 'https://cdn-icons-png.flaticon.com/512/2736/2736395.png', '#dc3545', TRUE, '2025-05-21 18:44:31'),
(5, 'Formula 1', 'The highest class of international racing', 'https://cdn-icons-png.flaticon.com/512/2736/2736398.png', '#6f42c1', TRUE, '2025-05-21 18:44:31');

-- =====================================================
-- 2. USERS
-- =====================================================
INSERT INTO users (id, username, email, password, full_name, profile_image, is_active, last_login, created_at, updated_at) VALUES
(1, 'Sachal Sumra', '<EMAIL>', '$2b$10$y/vSwZrUAdOaIQ0a.djuRu4hFNb1/m3a4lUpNzNWfwj6YJJo1uwBq', 'Maher Muhammad Sachal Sultan Sumra', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face', TRUE, NOW(), '2025-05-21 18:54:00', '2025-05-21 18:54:00'),
(2, 'testuser', '<EMAIL>', '$2b$10$5QvhVOj9lfQyHOUx3WZQZuXCxAMGVFIRGFDOVRfA4cy2OJlG1WFRi', 'Test User', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face', TRUE, NOW(), '2025-05-21 18:57:22', '2025-05-21 18:57:22');

-- =====================================================
-- 3. TEAMS
-- =====================================================
INSERT INTO teams (id, name, logo, category_id, country, description, founded_year, stadium, created_at) VALUES
-- Football Teams
(1, 'Manchester United', 'https://upload.wikimedia.org/wikipedia/en/7/7a/Manchester_United_FC_crest.svg', 1, 'England', 'Manchester United Football Club is a professional football club based in Manchester, England.', 1878, 'Old Trafford', '2025-05-21 18:57:22'),
(2, 'Barcelona FC', 'https://upload.wikimedia.org/wikipedia/en/4/47/FC_Barcelona_%28crest%29.svg', 1, 'Spain', 'Futbol Club Barcelona, commonly referred to as Barcelona and colloquially known as Barça.', 1899, 'Camp Nou', '2025-05-21 18:57:22'),
(3, 'Bayern Munich', 'https://upload.wikimedia.org/wikipedia/commons/1/1b/FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg', 1, 'Germany', 'FC Bayern Munich is a German professional sports club based in Munich, Bavaria.', 1900, 'Allianz Arena', '2025-05-21 18:57:22'),
(4, 'Paris Saint-Germain', 'https://upload.wikimedia.org/wikipedia/en/a/a7/Paris_Saint-Germain_F.C..svg', 1, 'France', 'Paris Saint-Germain Football Club, commonly referred to as Paris Saint-Germain, PSG, Paris or Paris SG.', 1970, 'Parc des Princes', '2025-05-21 18:57:22'),
(5, 'Liverpool FC', 'https://upload.wikimedia.org/wikipedia/en/0/0c/Liverpool_FC.svg', 1, 'England', 'Liverpool Football Club is a professional football club based in Liverpool, England.', 1892, 'Anfield', '2025-05-21 18:57:22'),
(16, 'Real Madrid', 'https://upload.wikimedia.org/wikipedia/en/5/56/Real_Madrid_CF.svg', 1, 'Spain', 'Real Madrid Club de Fútbol, commonly referred to as Real Madrid, is a Spanish professional football club based in Madrid.', 1902, 'Santiago Bernabéu', '2025-05-21 19:01:38'),
(17, 'Manchester City', 'https://upload.wikimedia.org/wikipedia/en/e/eb/Manchester_City_FC_badge.svg', 1, 'England', 'Manchester City Football Club is an English football club based in Manchester that competes in the Premier League.', 1880, 'Etihad Stadium', '2025-05-21 19:01:38'),
(18, 'Juventus', 'https://upload.wikimedia.org/wikipedia/commons/b/bc/Juventus_FC_2017_icon.svg', 1, 'Italy', 'Juventus Football Club, colloquially known as Juve, is a professional football club based in Turin, Piedmont, Italy.', 1897, 'Allianz Stadium', '2025-05-21 19:01:38'),
(19, 'Chelsea FC', 'https://upload.wikimedia.org/wikipedia/en/c/cc/Chelsea_FC.svg', 1, 'England', 'Chelsea Football Club is an English professional football club based in Fulham, West London.', 1905, 'Stamford Bridge', '2025-05-21 19:01:38'),

-- Basketball Teams
(6, 'LA Lakers', 'https://upload.wikimedia.org/wikipedia/commons/3/3c/Los_Angeles_Lakers_logo.svg', 2, 'USA', 'The Los Angeles Lakers are an American professional basketball team based in Los Angeles.', 1947, 'Crypto.com Arena', '2025-05-21 18:57:22'),
(7, 'Boston Celtics', 'https://upload.wikimedia.org/wikipedia/en/8/8f/Boston_Celtics.svg', 2, 'USA', 'The Boston Celtics are an American professional basketball team based in Boston.', 1946, 'TD Garden', '2025-05-21 18:57:22'),
(8, 'Chicago Bulls', 'https://upload.wikimedia.org/wikipedia/en/6/67/Chicago_Bulls_logo.svg', 2, 'USA', 'The Chicago Bulls are an American professional basketball team based in Chicago.', 1966, 'United Center', '2025-05-21 18:57:22'),
(9, 'Golden State Warriors', 'https://upload.wikimedia.org/wikipedia/en/0/01/Golden_State_Warriors_logo.svg', 2, 'USA', 'The Golden State Warriors are an American professional basketball team based in San Francisco.', 1946, 'Chase Center', '2025-05-21 18:57:22'),
(10, 'Miami Heat', 'https://upload.wikimedia.org/wikipedia/en/f/fb/Miami_Heat_logo.svg', 2, 'USA', 'The Miami Heat are an American professional basketball team based in Miami.', 1988, 'FTX Arena', '2025-05-21 18:57:22'),
(20, 'Brooklyn Nets', 'https://upload.wikimedia.org/wikipedia/commons/4/44/Brooklyn_Nets_newlogo.svg', 2, 'USA', 'The Brooklyn Nets are an American professional basketball team based in the New York City borough of Brooklyn.', 1967, 'Barclays Center', '2025-05-21 19:01:38'),
(21, 'Dallas Mavericks', 'https://upload.wikimedia.org/wikipedia/en/9/97/Dallas_Mavericks_logo.svg', 2, 'USA', 'The Dallas Mavericks are an American professional basketball team based in Dallas.', 1980, 'American Airlines Center', '2025-05-21 19:01:38'),
(22, 'Toronto Raptors', 'https://upload.wikimedia.org/wikipedia/en/3/36/Toronto_Raptors_logo.svg', 2, 'Canada', 'The Toronto Raptors are a Canadian professional basketball team based in Toronto.', 1995, 'Scotiabank Arena', '2025-05-21 19:01:38'),

-- Cricket Teams/Academies
(11, 'India', 'https://upload.wikimedia.org/wikipedia/en/8/8d/Cricket_India_Crest.svg', 3, 'India', 'The India men\'s national cricket team represents India in men\'s international cricket.', 1932, 'Various Stadiums', '2025-05-21 18:57:22'),
(12, 'Australia', 'https://upload.wikimedia.org/wikipedia/en/8/8c/Cricket_Australia.svg', 3, 'Australia', 'The Australia men\'s national cricket team represents Australia in men\'s international cricket.', 1877, 'Various Stadiums', '2025-05-21 18:57:22'),
(13, 'England', 'https://upload.wikimedia.org/wikipedia/en/5/5b/England_Cricket_Board.svg', 3, 'England', 'The England cricket team represents England and Wales in international cricket.', 1877, 'Lord\'s Cricket Ground', '2025-05-21 18:57:22'),
(14, 'Pakistan', 'https://upload.wikimedia.org/wikipedia/en/7/7b/Pakistan_Cricket_Board_Logo.svg', 3, 'Pakistan', 'The Pakistan men\'s national cricket team represents Pakistan in men\'s international cricket.', 1952, 'Various Stadiums', '2025-05-21 18:57:22'),
(15, 'South Africa', 'https://upload.wikimedia.org/wikipedia/en/d/dc/Cricket_South_Africa_logo.svg', 3, 'South Africa', 'The South Africa national cricket team represents South Africa in men\'s international cricket.', 1889, 'Various Stadiums', '2025-05-21 18:57:22'),
(49, 'Mumbai Indians', 'https://upload.wikimedia.org/wikipedia/en/2/2b/Mumbai_Indians_Logo.svg', 3, 'India', 'IPL cricket team from Mumbai', 2008, 'Wankhede Stadium', '2025-05-21 19:31:01'),

-- Tennis Teams/Academies
(51, 'Novak Tennis Academy', 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop', 4, 'Serbia', 'Tennis academy founded by Novak Djokovic', 2013, 'Belgrade Tennis Center', '2025-05-21 19:31:01'),

-- Formula 1 Teams
(52, 'Ferrari', 'https://upload.wikimedia.org/wikipedia/en/d/d1/Scuderia_Ferrari_logo.svg', 5, 'Italy', 'Italian Formula 1 racing team', 1950, 'Maranello Factory', '2025-05-21 19:31:01'),
(53, 'Mercedes AMG', 'https://upload.wikimedia.org/wikipedia/commons/9/90/Mercedes-Logo.svg', 5, 'Germany', 'German Formula 1 racing team', 2010, 'Brackley Factory', '2025-05-21 19:31:01');

-- =====================================================
-- 4. PLAYERS
-- =====================================================
INSERT INTO players (id, name, team_id, position, jersey_number, image, stats, created_at) VALUES
-- Manchester United Players
(1, 'Bruno Fernandes', 1, 'Midfielder', 8, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p141746.png', NULL, '2025-05-21 18:57:22'),
(2, 'Marcus Rashford', 1, 'Forward', 10, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p176297.png', NULL, '2025-05-21 18:57:22'),
(3, 'Harry Maguire', 1, 'Defender', 5, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p95658.png', NULL, '2025-05-21 18:57:22'),
(34, 'Bruno Fernandes', 1, 'Midfielder', 8, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p141746.png', NULL, '2025-05-21 19:15:50'),
(35, 'Marcus Rashford', 1, 'Forward', 10, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p176297.png', NULL, '2025-05-21 19:15:50'),
(36, 'Harry Maguire', 1, 'Defender', 5, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p95658.png', NULL, '2025-05-21 19:15:50'),
(67, 'Bruno Fernandes', 1, 'Midfielder', 8, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p141746.png', '{"goals": 15, "assists": 10, "appearances": 32}', '2025-05-21 19:31:01'),
(68, 'Marcus Rashford', 1, 'Forward', 10, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p176297.png', '{"goals": 12, "assists": 8, "appearances": 30}', '2025-05-21 19:31:01'),

-- Barcelona Players
(4, 'Pedri', 2, 'Midfielder', 8, 'https://www.fcbarcelona.com/photo-resources/2021/09/08/c4b1ef6e-1d83-4a0e-9a7e-0388890b341a/mini_PEDRI.png', NULL, '2025-05-21 18:57:22'),
(5, 'Gavi', 2, 'Midfielder', 6, 'https://www.fcbarcelona.com/photo-resources/2021/09/08/c4b1ef6e-1d83-4a0e-9a7e-0388890b341a/mini_GAVI.png', NULL, '2025-05-21 18:57:22'),
(6, 'Robert Lewandowski', 2, 'Forward', 9, 'https://www.fcbarcelona.com/photo-resources/2022/08/03/c0d192b3-b2c5-4b7c-9a65-14ad4f24a69c/09-ROBERT_LEWANDOWSKI.png', NULL, '2025-05-21 18:57:22'),
(37, 'Pedri', 2, 'Midfielder', 8, 'https://www.fcbarcelona.com/photo-resources/2021/09/08/c4b1ef6e-1d83-4a0e-9a7e-0388890b341a/mini_PEDRI.png', NULL, '2025-05-21 19:15:50'),
(38, 'Gavi', 2, 'Midfielder', 6, 'https://www.fcbarcelona.com/photo-resources/2021/09/08/c4b1ef6e-1d83-4a0e-9a7e-0388890b341a/mini_GAVI.png', NULL, '2025-05-21 19:15:50'),
(39, 'Robert Lewandowski', 2, 'Forward', 9, 'https://www.fcbarcelona.com/photo-resources/2022/08/03/c0d192b3-b2c5-4b7c-9a65-14ad4f24a69c/09-ROBERT_LEWANDOWSKI.png', NULL, '2025-05-21 19:15:50'),
(69, 'Frenkie de Jong', 2, 'Midfielder', 21, 'https://www.fcbarcelona.com/photo-resources/2022/08/03/c0d192b3-b2c5-4b7c-9a65-14ad4f24a69c/21-FRENKIE_DE_JONG.png', '{"goals": 5, "assists": 12, "appearances": 34}', '2025-05-21 19:31:01'),

-- Bayern Munich Players
(25, 'Thomas Müller', 3, 'Forward', 25, 'https://img.fcbayern.com/image/upload/t_cms-1x1-seo/v1656420334/cms/public/images/fcbayern-com/players/spielerportraits/thomas_mueller.png', NULL, '2025-05-21 19:01:38'),
(26, 'Joshua Kimmich', 3, 'Midfielder', 6, 'https://img.fcbayern.com/image/upload/t_cms-1x1-seo/v1656420334/cms/public/images/fcbayern-com/players/spielerportraits/joshua_kimmich.png', NULL, '2025-05-21 19:01:38'),
(27, 'Manuel Neuer', 3, 'Goalkeeper', 1, 'https://img.fcbayern.com/image/upload/t_cms-1x1-seo/v1656420334/cms/public/images/fcbayern-com/players/spielerportraits/manuel_neuer.png', NULL, '2025-05-21 19:01:38'),
(58, 'Thomas Müller', 3, 'Forward', 25, 'https://img.fcbayern.com/image/upload/t_cms-1x1-seo/v1656420334/cms/public/images/fcbayern-com/players/spielerportraits/thomas_mueller.png', NULL, '2025-05-21 19:16:38'),
(59, 'Joshua Kimmich', 3, 'Midfielder', 6, 'https://img.fcbayern.com/image/upload/t_cms-1x1-seo/v1656420334/cms/public/images/fcbayern-com/players/spielerportraits/joshua_kimmich.png', NULL, '2025-05-21 19:16:38'),
(60, 'Manuel Neuer', 3, 'Goalkeeper', 1, 'https://img.fcbayern.com/image/upload/t_cms-1x1-seo/v1656420334/cms/public/images/fcbayern-com/players/spielerportraits/manuel_neuer.png', NULL, '2025-05-21 19:16:38'),

-- Liverpool Players
(22, 'Mohamed Salah', 5, 'Forward', 11, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p118748.png', NULL, '2025-05-21 19:01:38'),
(23, 'Virgil van Dijk', 5, 'Defender', 4, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p97032.png', NULL, '2025-05-21 19:01:38'),
(24, 'Alisson Becker', 5, 'Goalkeeper', 1, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p116535.png', NULL, '2025-05-21 19:01:38'),
(55, 'Mohamed Salah', 5, 'Forward', 11, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p118748.png', NULL, '2025-05-21 19:16:38'),
(56, 'Virgil van Dijk', 5, 'Defender', 4, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p97032.png', NULL, '2025-05-21 19:16:38'),
(57, 'Alisson Becker', 5, 'Goalkeeper', 1, 'https://resources.premierleague.com/premierleague/photos/players/250x250/p116535.png', NULL, '2025-05-21 19:16:38'),

-- LA Lakers Players
(7, 'LeBron James', 6, 'Forward', 23, 'https://cdn.nba.com/headshots/nba/latest/1040x760/2544.png', NULL, '2025-05-21 18:57:22'),
(8, 'Anthony Davis', 6, 'Forward', 3, 'https://cdn.nba.com/headshots/nba/latest/1040x760/203076.png', NULL, '2025-05-21 18:57:22'),
(9, 'Russell Westbrook', 6, 'Guard', 0, 'https://cdn.nba.com/headshots/nba/latest/1040x760/201566.png', NULL, '2025-05-21 18:57:22'),
(40, 'LeBron James', 6, 'Forward', 23, 'https://cdn.nba.com/headshots/nba/latest/1040x760/2544.png', NULL, '2025-05-21 19:15:50'),
(41, 'Anthony Davis', 6, 'Forward', 3, 'https://cdn.nba.com/headshots/nba/latest/1040x760/203076.png', NULL, '2025-05-21 19:15:50'),
(42, 'Russell Westbrook', 6, 'Guard', 0, 'https://cdn.nba.com/headshots/nba/latest/1040x760/201566.png', NULL, '2025-05-21 19:15:50'),
(70, 'LeBron James', 3, 'Forward', 23, 'lebron.png', '{"points": 25.5, "assists": 10.2, "rebounds": 7.9}', '2025-05-21 19:31:01'),

-- Boston Celtics Players
(28, 'Jayson Tatum', 7, 'Forward', 0, 'https://cdn.nba.com/headshots/nba/latest/1040x760/1628369.png', NULL, '2025-05-21 19:01:38'),
(29, 'Jaylen Brown', 7, 'Guard-Forward', 7, 'https://cdn.nba.com/headshots/nba/latest/1040x760/1627759.png', NULL, '2025-05-21 19:01:38'),
(30, 'Marcus Smart', 7, 'Guard', 36, 'https://cdn.nba.com/headshots/nba/latest/1040x760/203935.png', NULL, '2025-05-21 19:01:38'),
(61, 'Jayson Tatum', 7, 'Forward', 0, 'https://cdn.nba.com/headshots/nba/latest/1040x760/1628369.png', NULL, '2025-05-21 19:16:38'),
(62, 'Jaylen Brown', 7, 'Guard-Forward', 7, 'https://cdn.nba.com/headshots/nba/latest/1040x760/1627759.png', NULL, '2025-05-21 19:16:38'),
(63, 'Marcus Smart', 7, 'Guard', 36, 'https://cdn.nba.com/headshots/nba/latest/1040x760/203935.png', NULL, '2025-05-21 19:16:38'),
(71, 'Jayson Tatum', 4, 'Forward', 0, 'tatum.png', '{"points": 26.8, "assists": 4.5, "rebounds": 8.2}', '2025-05-21 19:31:01'),

-- Golden State Warriors Players
(31, 'Stephen Curry', 9, 'Guard', 30, 'https://cdn.nba.com/headshots/nba/latest/1040x760/201939.png', NULL, '2025-05-21 19:01:38'),
(32, 'Klay Thompson', 9, 'Guard', 11, 'https://cdn.nba.com/headshots/nba/latest/1040x760/202691.png', NULL, '2025-05-21 19:01:38'),
(33, 'Draymond Green', 9, 'Forward', 23, 'https://cdn.nba.com/headshots/nba/latest/1040x760/203110.png', NULL, '2025-05-21 19:01:38'),
(64, 'Stephen Curry', 9, 'Guard', 30, 'https://cdn.nba.com/headshots/nba/latest/1040x760/201939.png', NULL, '2025-05-21 19:16:38'),
(65, 'Klay Thompson', 9, 'Guard', 11, 'https://cdn.nba.com/headshots/nba/latest/1040x760/202691.png', NULL, '2025-05-21 19:16:38'),
(66, 'Draymond Green', 9, 'Forward', 23, 'https://cdn.nba.com/headshots/nba/latest/1040x760/203110.png', NULL, '2025-05-21 19:16:38'),

-- Cricket Players
(10, 'Virat Kohli', 11, 'Batsman', 18, 'https://www.cricbuzz.com/a/img/v1/152x152/i1/c170661/virat-kohli.jpg', NULL, '2025-05-21 18:57:22'),
(11, 'Rohit Sharma', 11, 'Batsman', 45, 'https://www.cricbuzz.com/a/img/v1/152x152/i1/c170658/rohit-sharma.jpg', NULL, '2025-05-21 18:57:22'),
(12, 'Jasprit Bumrah', 11, 'Bowler', 93, 'https://www.cricbuzz.com/a/img/v1/152x152/i1/c170684/jasprit-bumrah.jpg', NULL, '2025-05-21 18:57:22'),
(43, 'Virat Kohli', 11, 'Batsman', 18, 'https://www.cricbuzz.com/a/img/v1/152x152/i1/c170661/virat-kohli.jpg', NULL, '2025-05-21 19:15:50'),
(44, 'Rohit Sharma', 11, 'Batsman', 45, 'https://www.cricbuzz.com/a/img/v1/152x152/i1/c170658/rohit-sharma.jpg', NULL, '2025-05-21 19:15:50'),
(45, 'Jasprit Bumrah', 11, 'Bowler', 93, 'https://www.cricbuzz.com/a/img/v1/152x152/i1/c170684/jasprit-bumrah.jpg', NULL, '2025-05-21 19:15:50'),
(72, 'Rohit Sharma', 5, 'Batsman', 45, 'rohit.png', '{"runs": 4500, "average": 35.2, "strike_rate": 140.5}', '2025-05-21 19:31:01'),
(73, 'Steve Smith', 6, 'Batsman', 49, 'smith.png', '{"runs": 7500, "average": 61.3, "centuries": 27}', '2025-05-21 19:31:01'),

-- Real Madrid Players
(13, 'Vinicius Jr', 16, 'Forward', 7, 'https://img.a.transfermarkt.technology/portrait/big/371998-1661412629.jpg', NULL, '2025-05-21 19:01:38'),
(14, 'Jude Bellingham', 16, 'Midfielder', 5, 'https://img.a.transfermarkt.technology/portrait/big/581678-1686142504.jpg', NULL, '2025-05-21 19:01:38'),
(15, 'Thibaut Courtois', 16, 'Goalkeeper', 1, 'https://img.a.transfermarkt.technology/portrait/big/108390-1661412629.jpg', NULL, '2025-05-21 19:01:38'),
(46, 'Vinicius Jr', 16, 'Forward', 7, 'https://img.a.transfermarkt.technology/portrait/big/371998-1661412629.jpg', NULL, '2025-05-21 19:16:38'),
(47, 'Jude Bellingham', 16, 'Midfielder', 5, 'https://img.a.transfermarkt.technology/portrait/big/581678-1686142504.jpg', NULL, '2025-05-21 19:16:38'),
(48, 'Thibaut Courtois', 16, 'Goalkeeper', 1, 'https://img.a.transfermarkt.technology/portrait/big/108390-1661412629.jpg', NULL, '2025-05-21 19:16:38'),

-- Manchester City Players
(16, 'Erling Haaland', 17, 'Forward', 9, 'https://img.a.transfermarkt.technology/portrait/big/418560-1656179352.jpg', NULL, '2025-05-21 19:01:38'),
(17, 'Kevin De Bruyne', 17, 'Midfielder', 17, 'https://img.a.transfermarkt.technology/portrait/big/88755-1661412629.jpg', NULL, '2025-05-21 19:01:38'),
(18, 'Phil Foden', 17, 'Midfielder', 47, 'https://img.a.transfermarkt.technology/portrait/big/406635-1661412629.jpg', NULL, '2025-05-21 19:01:38'),
(49, 'Erling Haaland', 17, 'Forward', 9, 'https://img.a.transfermarkt.technology/portrait/big/418560-1656179352.jpg', NULL, '2025-05-21 19:16:38'),
(50, 'Kevin De Bruyne', 17, 'Midfielder', 17, 'https://img.a.transfermarkt.technology/portrait/big/88755-1661412629.jpg', NULL, '2025-05-21 19:16:38'),
(51, 'Phil Foden', 17, 'Midfielder', 47, 'https://img.a.transfermarkt.technology/portrait/big/406635-1661412629.jpg', NULL, '2025-05-21 19:16:38'),

-- Brooklyn Nets Players
(19, 'Kevin Durant', 20, 'Forward', 7, 'https://cdn.nba.com/headshots/nba/latest/1040x760/201142.png', NULL, '2025-05-21 19:01:38'),
(20, 'Kyrie Irving', 20, 'Guard', 11, 'https://cdn.nba.com/headshots/nba/latest/1040x760/202681.png', NULL, '2025-05-21 19:01:38'),
(21, 'Ben Simmons', 20, 'Guard-Forward', 10, 'https://cdn.nba.com/headshots/nba/latest/1040x760/1627732.png', NULL, '2025-05-21 19:01:38'),
(52, 'Kevin Durant', 20, 'Forward', 7, 'https://cdn.nba.com/headshots/nba/latest/1040x760/201142.png', NULL, '2025-05-21 19:16:38'),
(53, 'Kyrie Irving', 20, 'Guard', 11, 'https://cdn.nba.com/headshots/nba/latest/1040x760/202681.png', NULL, '2025-05-21 19:16:38'),
(54, 'Ben Simmons', 20, 'Guard-Forward', 10, 'https://cdn.nba.com/headshots/nba/latest/1040x760/1627732.png', NULL, '2025-05-21 19:16:38'),

-- Tennis and F1 Players
(74, 'Rafael Nadal', 7, 'Professional', 0, 'nadal.png', '{"grand_slams": 22, "win_percentage": 83.2}', '2025-05-21 19:31:01'),
(75, 'Charles Leclerc', 8, 'Driver', 16, 'leclerc.png', '{"wins": 5, "poles": 18, "podiums": 16}', '2025-05-21 19:31:01'),
(76, 'Lewis Hamilton', 9, 'Driver', 44, 'hamilton.png', '{"wins": 103, "podiums": 191, "championships": 7}', '2025-05-21 19:31:01');

-- =====================================================
-- 5. NEWS ARTICLES
-- =====================================================
INSERT INTO news_articles (id, title, content, excerpt, image, author, category_id, published_at, created_at) VALUES
(1, 'Manchester United Secures Dramatic Win Against Liverpool', 'In a thrilling encounter at Old Trafford, Manchester United secured a dramatic 3-2 win against Liverpool. Bruno Fernandes scored a stunning free-kick in the 89th minute to seal the victory for the Red Devils.\n\nThe match started with Liverpool taking an early lead through Mohamed Salah in the 10th minute. However, Manchester United quickly responded with Marcus Rashford equalizing in the 15th minute. The first half ended with both teams locked at 1-1.\n\nIn the second half, Manchester United took the lead through a well-worked goal finished by Jadon Sancho. Liverpool fought back and equalized through a Salah penalty in the 75th minute. Just when it seemed like the match would end in a draw, Bruno Fernandes stepped up to score a magnificent free-kick from 25 yards out, sending the Old Trafford crowd into raptures.\n\nThis victory moves Manchester United up to 4th in the Premier League table, while Liverpool remains in 6th place.', 'A thrilling encounter at Old Trafford ends in dramatic fashion', 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=600&fit=crop', 'John Smith', 1, '2025-05-19 18:57:22', '2025-05-21 18:57:22'),

(2, 'Lakers Dominate Warriors in NBA Showdown', 'The Los Angeles Lakers put on a show against the Golden State Warriors, winning 120-100 in a dominant performance. LeBron James led the way with a triple-double: 30 points, 12 rebounds, and 10 assists.\n\nThe Lakers started strong, taking a 35-22 lead in the first quarter and never looked back. Anthony Davis was a force on both ends of the floor, contributing 28 points and 5 blocks. Russell Westbrook added 18 points and 8 assists off the bench.\n\nFor the Warriors, Stephen Curry scored 27 points but struggled from three-point range, shooting just 3-12. Klay Thompson added 18 points, but it wasn\'t enough to keep up with the Lakers\' firepower.\n\nThis victory extends the Lakers\' winning streak to five games, solidifying their position at the top of the Western Conference.', 'Lakers showcase dominance in Western Conference clash', 'https://images.unsplash.com/photo-**********-68e109498ffc?w=800&h=600&fit=crop', 'Michael Johnson', 2, '2025-05-18 18:57:22', '2025-05-21 18:57:22'),

(3, 'India Defeats Australia in Cricket World Cup Thriller', 'In a nail-biting encounter at the Cricket World Cup, India defeated Australia by 5 runs in the last over. Virat Kohli was named Player of the Match for his brilliant century.\n\nAfter winning the toss, India chose to bat first and posted a competitive total of 315/6 in their 50 overs. Virat Kohli led the charge with a magnificent 120 off 125 balls, while Rohit Sharma contributed 65 runs. For Australia, Mitchell Starc was the pick of the bowlers with figures of 3/60.\n\nIn reply, Australia started well with David Warner and Steve Smith putting on a 100-run partnership. However, Jasprit Bumrah\'s exceptional death bowling restricted Australia to 310/8, giving India a narrow victory.\n\nThis win puts India at the top of the World Cup standings, with Australia in second place.', 'India edges past Australia in World Cup thriller', 'https://images.unsplash.com/photo-*************-19e32dc3e97e?w=800&h=600&fit=crop', 'Rahul Sharma', 3, '2025-05-20 18:57:22', '2025-05-21 18:57:22'),

(4, 'Barcelona\'s New Signing Impresses in Debut Match', 'Barcelona\'s latest signing made an impressive debut in the 3-0 victory against Sevilla at Camp Nou. The new star scored one goal and provided an assist, immediately winning over the fans.\n\nThe match saw Barcelona dominate from the start, with Robert Lewandowski opening the scoring in the 12th minute. The new signing doubled the lead in the 34th minute with a spectacular long-range effort, before setting up Pedri for the third goal in the second half.\n\nCoach Xavi Hernandez was full of praise for the new addition: "He has adapted quickly to our style of play. His technical ability and vision are exceptional, and he already has a good understanding with his teammates."\n\nBarcelona now sits just two points behind league leaders Real Madrid, with a game in hand.', 'New Barcelona signing shines in debut victory', 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800&h=600&fit=crop', 'Carlos Rodriguez', 1, '2025-05-17 18:57:22', '2025-05-21 18:57:22'),

(5, 'NBA Announces All-Star Game Starters', 'The NBA has officially announced the starters for this year\'s All-Star Game, with LeBron James and Giannis Antetokounmpo named as team captains.\n\nJoining LeBron James in the Western Conference starting lineup are Stephen Curry (Golden State Warriors), Luka Dončić (Dallas Mavericks), Nikola Jokić (Denver Nuggets), and Anthony Davis (Los Angeles Lakers).\n\nFor the Eastern Conference, Giannis Antetokounmpo will be joined by Kevin Durant (Brooklyn Nets), Jayson Tatum (Boston Celtics), Joel Embiid (Philadelphia 76ers), and Donovan Mitchell (Cleveland Cavaliers).\n\nThis marks LeBron James\' 19th consecutive All-Star selection, extending his record for the most consecutive appearances. The All-Star Game will take place next month at the United Center in Chicago.', 'NBA reveals All-Star Game starting lineups', 'https://images.unsplash.com/photo-1574623452334-1e0ac2b3ccb4?w=800&h=600&fit=crop', 'Jessica Williams', 2, '2025-05-16 18:57:22', '2025-05-21 18:57:22'),

(21, 'Manchester United Wins Derby', 'Manchester United secured a 2-1 victory against Manchester City in an exciting derby match.', 'Manchester United triumphs in Manchester derby', 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=800&h=600&fit=crop', 'John Smith', 1, '2025-05-21 19:31:01', '2025-05-21 19:31:01'),
(22, 'NBA Finals Set to Begin', 'The LA Lakers and Boston Celtics will face off in the NBA Finals starting next week.', 'Historic rivals meet in NBA Finals', 'https://images.unsplash.com/photo-*************-7546f13df25d?w=800&h=600&fit=crop', 'Mike Johnson', 2, '2025-05-21 19:31:01', '2025-05-21 19:31:01'),
(23, 'Cricket World Cup Preview', 'A comprehensive preview of the upcoming Cricket World Cup with analysis of all teams.', 'World Cup cricket tournament preview', 'https://images.unsplash.com/photo-*************-48f60103fc96?w=800&h=600&fit=crop', 'Rahul Sharma', 3, '2025-05-21 19:31:01', '2025-05-21 19:31:01'),
(24, 'Nadal Wins French Open', 'Rafael Nadal wins his 15th French Open title in an epic five-set final.', 'Nadal makes history at Roland Garros', 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop', 'Tennis Reporter', 4, '2025-05-21 19:31:01', '2025-05-21 19:31:01'),
(25, 'Ferrari Unveils New Car', 'Ferrari has unveiled their new car for the upcoming Formula 1 season with significant aerodynamic improvements.', 'Ferrari reveals new F1 challenger', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', 'F1 Insider', 5, '2025-05-21 19:31:01', '2025-05-21 19:31:01');

-- =====================================================
-- 6. FANTASY LEAGUES
-- =====================================================
INSERT INTO fantasy_leagues (id, name, description, category_id, start_date, end_date, max_teams, created_by, created_at) VALUES
(1, 'Premier League Fantasy', 'Compete with other managers in this fantasy league based on the English Premier League.', 1, '2025-05-21', '2025-11-21', 20, NULL, '2025-05-21 18:57:22'),
(2, 'NBA Fantasy Challenge', 'Build your dream NBA team and compete for the top spot in our basketball fantasy league.', 2, '2025-04-21', '2025-10-21', 15, NULL, '2025-05-21 18:57:22'),
(3, 'Cricket World Cup Fantasy', 'Select your best XI from the world\'s top cricket players and compete in our fantasy cricket league.', 3, '2025-06-21', '2025-08-21', 25, NULL, '2025-05-21 18:57:22'),
(4, 'Champions League Fantasy', 'Create your ultimate European football team in this Champions League fantasy competition.', 1, '2025-03-21', '2025-07-21', 20, NULL, '2025-05-21 18:57:22'),
(5, 'NBA All-Star Fantasy', 'Special fantasy league for the NBA All-Star weekend. Pick your stars and earn points based on their performance.', 2, '2025-07-21', '2025-08-21', 10, NULL, '2025-05-21 18:57:22'),
(6, 'La Liga Fantasy', 'Build your dream team from Spanish La Liga players and compete against other managers.', 1, '2025-05-21', '2026-01-21', 20, NULL, '2025-05-21 19:01:38'),
(7, 'NBA Playoffs Fantasy', 'Special fantasy league for the NBA playoffs. Pick your stars and earn points based on their playoff performance.', 2, '2025-06-21', '2025-08-21', 16, NULL, '2025-05-21 19:01:38'),
(8, 'Tennis Grand Slam Fantasy', 'Create your fantasy tennis team for all four Grand Slam tournaments.', 4, '2025-04-21', '2026-04-21', 20, NULL, '2025-05-21 19:01:38'),
(9, 'F1 Constructor Challenge', 'Build your own Formula 1 team by selecting drivers and constructors.', 5, '2025-05-21', '2026-02-21', 15, NULL, '2025-05-21 19:01:38'),
(13, 'Champions League Fantasy', 'Create your ultimate European football team in this Champions League fantasy competition.', 1, '2025-03-21', '2025-07-21', 20, NULL, '2025-05-21 19:15:50');

-- =====================================================
-- 7. GAME SCHEDULES
-- =====================================================
INSERT INTO game_schedules (id, home_team_id, away_team_id, game_time, venue, status, score_home, score_away, category_id, stream_url, created_at) VALUES
-- Football Games
(1, 1, 5, '2025-05-23 18:57:22', 'Old Trafford, Manchester', 'scheduled', NULL, NULL, 1, 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(2, 2, 3, '2025-05-21 15:57:22', 'Camp Nou, Barcelona', 'live', 2, 1, 1, 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(3, 4, 1, '2025-05-19 18:57:22', 'Parc des Princes, Paris', 'completed', 1, 3, 1, 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(10, 16, 17, '2025-05-21 18:01:38', 'Santiago Bernabeu, Madrid', 'live', 2, 2, 1, 'https://images.unsplash.com/photo-1543353071-10c8ba85a904?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(11, 18, 19, '2025-05-24 19:01:38', 'Allianz Stadium, Turin', 'scheduled', NULL, NULL, 1, 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(12, 17, 5, '2025-05-20 19:01:38', 'Etihad Stadium, Manchester', 'completed', 3, 1, 1, 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(43, 1, 2, '2025-06-01 20:00:00', 'Old Trafford', 'scheduled', NULL, NULL, 1, 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=800&h=600&fit=crop', '2025-05-21 19:31:01'),
(47, 2, 1, '2025-06-15 20:45:00', 'Camp Nou', 'scheduled', NULL, NULL, 1, 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=600&fit=crop', '2025-05-21 19:31:01'),

-- Basketball Games
(4, 6, 7, '2025-05-21 17:57:22', 'Staples Center, Los Angeles', 'live', 78, 72, 2, 'https://images.unsplash.com/photo-**********-68e109498ffc?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(5, 8, 9, '2025-05-22 18:57:22', 'United Center, Chicago', 'scheduled', NULL, NULL, 2, 'https://images.unsplash.com/photo-*************-7546f13df25d?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(6, 10, 6, '2025-05-18 18:57:22', 'American Airlines Arena, Miami', 'completed', 95, 105, 2, 'https://images.unsplash.com/photo-**********-68e109498ffc?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(13, 20, 21, '2025-05-21 17:01:38', 'Barclays Center, Brooklyn', 'live', 95, 92, 2, 'https://images.unsplash.com/photo-*************-7546f13df25d?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(14, 22, 8, '2025-05-23 19:01:38', 'Scotiabank Arena, Toronto', 'scheduled', NULL, NULL, 2, 'https://images.unsplash.com/photo-**********-68e109498ffc?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(15, 7, 9, '2025-05-21 15:01:38', 'TD Garden, Boston', 'completed', 112, 108, 2, 'https://images.unsplash.com/photo-*************-7546f13df25d?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(44, 3, 4, '2025-05-25 19:30:00', 'Staples Center', 'completed', 105, 98, 2, 'https://images.unsplash.com/photo-**********-68e109498ffc?w=800&h=600&fit=crop', '2025-05-21 19:31:01'),

-- Cricket Games
(7, 11, 12, '2025-05-26 18:57:22', 'Eden Gardens, Kolkata', 'scheduled', NULL, NULL, 3, 'https://images.unsplash.com/photo-*************-19e32dc3e97e?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(8, 13, 14, '2025-05-21 16:57:22', 'Lord\'s Cricket Ground, London', 'live', 245, 187, 3, 'https://images.unsplash.com/photo-*************-48f60103fc96?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(9, 15, 11, '2025-05-20 18:57:22', 'Newlands Cricket Ground, Cape Town', 'completed', 275, 278, 3, 'https://images.unsplash.com/photo-*************-19e32dc3e97e?w=800&h=600&fit=crop', '2025-05-21 18:57:22'),
(45, 5, 6, '2025-05-30 14:00:00', 'Wankhede Stadium', 'scheduled', NULL, NULL, 3, 'https://images.unsplash.com/photo-*************-48f60103fc96?w=800&h=600&fit=crop', '2025-05-21 19:31:01'),

-- Tennis Games
(16, 1, 2, '2025-05-21 18:31:38', 'Roland Garros, Paris', 'live', 2, 1, 4, 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(17, 3, 4, '2025-05-22 19:01:38', 'Wimbledon, London', 'scheduled', NULL, NULL, 4, 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(18, 5, 6, '2025-05-21 14:01:38', 'US Open, New York', 'completed', 3, 2, 4, 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),

-- Formula 1 Races
(19, 7, 8, '2025-05-21 18:16:38', 'Monaco Grand Prix, Monte Carlo', 'live', NULL, NULL, 5, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(20, 9, 10, '2025-05-28 19:01:38', 'Italian Grand Prix, Monza', 'scheduled', NULL, NULL, 5, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(21, 11, 12, '2025-05-14 19:01:38', 'British Grand Prix, Silverstone', 'completed', NULL, NULL, 5, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', '2025-05-21 19:01:38'),
(46, 8, 9, '2025-06-07 15:00:00', 'Monaco Circuit', 'scheduled', NULL, NULL, 5, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', '2025-05-21 19:31:01');

-- =====================================================
-- 8. FANTASY TEAMS
-- =====================================================
INSERT INTO fantasy_teams (id, name, user_id, league_id, points, team_rank, created_at) VALUES
(1, 'Test FC', 1, 1, 120, 5, '2025-05-21 18:57:22'),
(2, 'Hoop Dreams', 1, 2, 85, 8, '2025-05-21 18:57:22'),
(3, 'Test', 1, 4, 0, NULL, '2025-05-21 19:18:34'),
(4, 'test', 1, 3, 0, NULL, '2025-05-22 09:05:21'),
(5, 'crhj', 1, 13, 0, NULL, '2025-06-04 14:41:45');

-- =====================================================
-- 9. FANTASY TEAM PLAYERS
-- =====================================================
INSERT INTO fantasy_team_players (id, team_id, player_id, created_at) VALUES
(1, 1, 1, '2025-05-21 18:57:22'),
(2, 1, 2, '2025-05-21 18:57:22'),
(3, 1, 3, '2025-05-21 18:57:22'),
(4, 2, 10, '2025-05-21 18:57:22'),
(5, 2, 11, '2025-05-21 18:57:22'),
(6, 2, 12, '2025-05-21 18:57:22'),
(8, 3, 3, '2025-05-21 19:18:42'),
(9, 3, 34, '2025-05-21 19:18:48'),
(10, 4, 12, '2025-05-22 09:05:38'),
(11, 5, 1, '2025-06-04 14:41:57'),
(12, 5, 34, '2025-06-04 14:42:03');

-- =====================================================
-- DATA IMPORT SUMMARY
-- =====================================================
-- Total Records Imported:
-- - Sports Categories: 5
-- - Users: 2
-- - Teams: 53
-- - Players: 76
-- - News Articles: 25
-- - Fantasy Leagues: 10
-- - Game Schedules: 47
-- - Fantasy Teams: 5
-- - Fantasy Team Players: 12
--
-- Total: 235+ records across all tables
--
-- Note: This data is imported from the CSV files in the root directory
-- All foreign key relationships are maintained
-- =====================================================
